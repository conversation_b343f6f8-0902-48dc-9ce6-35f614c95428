# Complete Command Reference

## Mouse Commands

### Click Commands
- `click` - Simple left click
- `click x y` - Click at coordinates (x,y)
- `click right` - Right click
- `click middle` - Middle click (also: `click middleclick`, `click mclick`)
- `click shift` - Shift + left click
- `click double` - Double left click
- `click dshift` - Double shift click (also: `click doubleshift`)
- `click triple` - Triple left click
- `click right x y` - Right click at coordinates
- `click middle x y` - Middle click at coordinates
- `click shift x y` - Shift + click at coordinates
- `click double x y` - Double click at coordinates
- `click dshift x y` - Double shift click at coordinates
- `click triple x y` - Triple click at coordinates
- `click rel x y` - Click relative to current position (also: `click relative`, `click delta`, `click offset`)
- `click dur time x y` - Click at (x,y) with movement duration (also: `click duration`, `click time`, `click delay`)

### Move Commands
- `move x y` - Move to coordinates (also: `moveto`)
- `move rel x y` - Move relative to current position (also: `move relative`, `move delta`, `move offset`)
- `move dur time x y` - Move to coordinates over specified duration (also: `move duration`, `move time`, `move delay`)

## Keyboard Commands

### Typing Commands
- `type "text"` - Type text instantly
- `typef delay "text"` - Type text with delay between characters
- `press key` - Press a single key
- `hotkey key1 key2` - Press multiple keys simultaneously (e.g., `hotkey ctrl c`)

### Special Key Commands
- `copy` - Press Ctrl+C
- `paste` - Press Ctrl+V

## System Commands

### Wait Commands
- `wait seconds` - Wait for specified seconds

### Browser Commands
- `url address` - Open URL in browser (also: `gotourl`, `browser`)
- `search query` - Search Google for query

### System Integration
- `console command` - Run console command (also: `terminal`)
- `python code` - Execute Python code

## Function Definition

### Syntax
```
func name(paramCount) {
    commands
}
```

### Parameter Usage
- Parameters referenced as $1 through $9
- Example:
```
func clickAt(2) {
    move $1 $2
    wait 0.5
    click
}
```

## Command Modifiers

Command Modifier Definition: A  special keyword that can be added to a command to change its behavior.
Command Modifier Syntax: `command # -modifier`

### Threading
- Add `-thread` to run command in separate thread allowing multiple things to happen at the same time.
- Example: `click 100 200 # -thread`

### Repetition
- Add `-x[number]` to repeat command
- Example: `click 100 200 # -x5`

## Builtin Functions

### Syntax
- Builtin functions are called using the `$` prefix
- Format: `$functionName(arguments)`
- Can be used within any command or as part of expressions

### Available Functions

#### RGB Function
```
$rgb(x, y)
```
- Gets RGB color value at screen coordinates (x,y)
- Returns format: "(r, g, b)"
- Example: `$rgb(100, 200)` might return "(255, 128, 0)"

#### Math Function
```
$math(expression)
```
- Evaluates mathematical expressions
- Supports basic arithmetic operations
- Safe evaluation (no access to Python builtins)
- Examples:
  - `$math(100 + 200)`
  - `$math(5 * 10)`
  - `$math(2 ** 3)`

### Using Builtin Functions

#### In Commands
```
move $math(100 + 50) $math(200 + 25)
type "Color at cursor: $rgb($math(100), 200)"
```

#### In Variables
```
color = $rgb(100, 100)
x = $math(100 * 2)
```

### Builtin Function Notes
- Builtin functions are evaluated before command execution
- Can be nested within other commands
- Return values are converted to strings
- Math expressions are safely evaluated
- RGB values are screen-dependent

## General Notes
- All coordinates are in pixels
- Times/delays are in seconds
- Text must be in double quotes
- Commands are case-insensitive
- Coordinates can be written as: `100 200` or `(100, 200)` or `100, 200`
- Comments start with `#`
- Modifier commands can be added to any command
