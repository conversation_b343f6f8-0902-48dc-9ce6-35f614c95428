import mss
import mss.tools
from PIL import Image
import numpy as np
import time
from datetime import datetime
import pyautogui
from pynput import keyboard

# --- CONFIGURATION ---
COIN_REF_PIXEL = (844, 704)

TAILS_RGB = (35, 38, 43)
HEADS_RGB = (74, 49, 255)

COLOR_MATCH_THRESHOLD = 20 # Adjust carefully!

# This duration must now be AT LEAST the full animation time plus a safety buffer.
ANIMATION_DURATION = 3.175

# Coordinates for the "Flip" button (X, Y)
FLIP_BUTTON_COORDS = (1408, 840)

LOG_FILE = 'coin_toss_automated_log.csv'

# --- GLOBAL FLAG TO CONTROL LOOP ---
running = True

# Initialize screenshotter
sct = mss.mss()

# --- Helper Functions ---

def get_pixel_color(pixel_coords):
    """Captures a 1x1 pixel region at pixel_coords and returns its RGB color."""
    monitor_area = {
        'top': pixel_coords[1],
        'left': pixel_coords[0],
        'width': 1,
        'height': 1
    }
    sct_img = sct.grab(monitor_area)
    return Image.frombytes("RGB", sct_img.size, sct_img.rgb).getpixel((0, 0))

def is_color_match(actual_rgb, target_rgb, threshold):
    """Checks if actual_rgb is within the threshold of target_rgb."""
    r_diff = abs(actual_rgb[0] - target_rgb[0])
    g_diff = abs(actual_rgb[1] - target_rgb[1])
    b_diff = abs(actual_rgb[2] - target_rgb[2])
    return r_diff <= threshold and g_diff <= threshold and b_diff <= threshold

def determine_outcome():
    """Captures the coin pixel and determines if it's Heads (1) or Tails (0)."""
    current_rgb = get_pixel_color(COIN_REF_PIXEL)
    
    # Debugging print - keep this in for now!
    print(f"DEBUG: Pixel color read: {current_rgb}")

    if is_color_match(current_rgb, HEADS_RGB, COLOR_MATCH_THRESHOLD):
        return 1 # Heads
    elif is_color_match(current_rgb, TAILS_RGB, COLOR_MATCH_THRESHOLD):
        return 0 # Tails
    else:
        # If neither matches after the main wait, something is wrong.
        # This could mean a transient color, or the threshold is too strict.
        return -1 # Unrecognized

# --- Global Listener for Keyboard Input (to stop the script) ---
def on_press(key):
    global running
    try:
        if key == keyboard.Key.esc:
            print("\nESC pressed. Stopping logging.")
            running = False
            return False
    except AttributeError:
        pass

# --- Main Logging Loop ---
def start_logging():
    global running

    print("--- Starting Automated Coin Toss Logging ---")
    print(f"Monitoring pixel: {COIN_REF_PIXEL}")
    print(f"Heads RGB: {HEADS_RGB}, Tails RGB: {TAILS_RGB}")
    print(f"Color Match Threshold: {COLOR_MATCH_THRESHOLD}")
    print(f"Animation Duration (wait time): {ANIMATION_DURATION} seconds")
    print(f"Flip button at: {FLIP_BUTTON_COORDS}")
    print(f"Logging to: {LOG_FILE}")
    print("\nIMPORTANT: Ensure Rugplay is the active/focused window for clicks to work!")
    print("To stop: Press the ESC key (from anywhere on your system).")
    print("------------------------------------------")

    listener = keyboard.Listener(on_press=on_press)
    listener.start()

    try:
        with open(LOG_FILE, 'a') as f:
            while running:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

                # 1. Click the "Flip" button
                pyautogui.click(FLIP_BUTTON_COORDS[0], FLIP_BUTTON_COORDS[1])
                print(f"[{timestamp}] Clicked Flip button.")

                # 2. Wait for the animation to fully complete and the new coin to settle indefinitely
                time.sleep(ANIMATION_DURATION) 
                
                # 3. Determine the outcome - one reliable check now that it's settled
                outcome = determine_outcome()

                # 4. Log the data
                if outcome != -1:
                    log_entry = f"{timestamp},{outcome}\n"
                    f.write(log_entry)
                    print(f"Logged: {timestamp}, Outcome: {outcome}")
                else:
                    print(f"[{timestamp}] Could not determine outcome for this flip. Last color read: {get_pixel_color(COIN_REF_PIXEL)}")
                    # If it's -1 here, it means the pixel color didn't match EITHER Heads or Tails RGB within the threshold.
                    # This could still be a calibration issue with COLOR_MATCH_THRESHOLD or a transient state.
                    # You might want to save a screenshot here for debugging:
                    # sct_img = sct.grab(sct.monitors[1]) # Or whichever monitor Rugplay is on
                    # mss.tools.to_png(sct_img.rgb, f"unrecognized_state_{timestamp.replace(':', '-')}.png")
                
                # A small delay before the next loop iteration to prevent excessive clicking/CPU usage
                # This should be enough to ensure the game is ready for the next click.
                time.sleep(0.5) # Reduced from 1.0, assuming ANIMATION_DURATION handles main wait

    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}")
        print("Please ensure the game window is visible and at the correct resolution/position.")
    finally:
        listener.stop()
        print("------------------------------------------")
        print("Logging process finished.")
        print("------------------------------------------")

if __name__ == "__main__":
    start_logging()