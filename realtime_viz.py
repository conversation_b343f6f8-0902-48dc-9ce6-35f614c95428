import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from collections import deque
import time
import os

# --- CONFIGURATION FOR VISUALIZATION ---
LOG_FILE = 'coin_toss_automated_log.csv'
ROLLING_WINDOW_SIZE = 100 # Calculate rolling average/win rate over the last 100 flips
MAX_FLIPS_TO_DISPLAY = 1000 # Max number of flips to show on the plot for clarity (can be adjusted)
UPDATE_INTERVAL_MS = 2000 # How often to update the plot, in milliseconds (e.g., 2000ms = 2 seconds)

# Assumed betting strategy (since your logger implies it)
# 1 = Heads, 0 = Tails
YOUR_IMPLIED_BET = 1 # We're assuming you're always betting Heads

# --- INITIALIZATION ---
# Deque for efficient rolling average (stores only the last N items)
# This will store the outcome of each flip (1 for Heads, 0 for Tails)
outcome_history = deque(maxlen=MAX_FLIPS_TO_DISPLAY)
win_loss_history = deque(maxlen=MAX_FLIPS_TO_DISPLAY) # 1 for win, 0 for loss

# Setup the plot
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8)) # Two subplots: one for overall, one for rolling win rate
plt.style.use('ggplot') # A nice visual style

# Set up plot titles and labels
ax1.set_title("Coin Toss Outcomes Over Time")
ax1.set_xlabel("Flip Number")
ax1.set_ylabel("Outcome (0=Tails, 1=Heads)")
ax1.set_yticks([0, 1])
ax1.set_yticklabels(['Tails', 'Heads'])
ax1.grid(True, linestyle='--', alpha=0.7)

ax2.set_title(f"Rolling Win Rate (Last {ROLLING_WINDOW_SIZE} Flips) & Overall Bias (Implied Bet: {'Heads' if YOUR_IMPLIED_BET == 1 else 'Tails'})")
ax2.set_xlabel("Flip Number")
ax2.set_ylabel("Percentage (%)")
ax2.set_ylim(0, 100)
ax2.axhline(50, color='gray', linestyle=':', linewidth=1, label='50% Mark') # 50% line
ax2.grid(True, linestyle='--', alpha=0.7)
ax2.legend()

# For reading file, keep track of last read position
last_read_position = 0
last_modified_time = 0

# --- FUNCTION TO READ NEW DATA ---
def read_new_data():
    global last_read_position, last_modified_time
    new_data = []

    # Check if file exists and has been modified since last read
    if not os.path.exists(LOG_FILE):
        print(f"Waiting for {LOG_FILE} to be created by the logging script...")
        return new_data # Return empty, file not ready

    current_modified_time = os.path.getmtime(LOG_FILE)

    if current_modified_time > last_modified_time:
        try:
            with open(LOG_FILE, 'r') as f:
                f.seek(last_read_position) # Move to where we last left off
                lines = f.readlines()
                for line in lines:
                    if ',' in line:
                        try:
                            # Assuming format: "timestamp,outcome"
                            parts = line.strip().split(',')
                            outcome = int(parts[1])
                            new_data.append(outcome)
                        except ValueError:
                            print(f"Skipping malformed line: {line.strip()}")
                last_read_position = f.tell() # Update last read position
            last_modified_time = current_modified_time # Update modified time
        except Exception as e:
            print(f"Error reading file: {e}")
    return new_data

# --- ANIMATION UPDATE FUNCTION ---
def animate(i):
    new_flips = read_new_data()

    if not new_flips:
        return # No new data, don't update plot

    for outcome in new_flips:
        outcome_history.append(outcome)
        # Determine win/loss based on implied bet
        win_loss_history.append(1 if outcome == YOUR_IMPLIED_BET else 0)

    # Clear previous plot content
    ax1.clear()
    ax2.clear()

    # --- Plotting Overall Outcomes (ax1) ---
    if outcome_history:
        flips = list(outcome_history)
        flip_numbers = range(len(flips))
        
        # Plot outcomes as discrete points (1 for Heads, 0 for Tails)
        ax1.plot(flip_numbers, flips, 'o', markersize=4, label='Actual Outcome')
        
        # Calculate overall Heads/Tails percentage
        total_flips = len(flips)
        heads_count = sum(flips)
        tails_count = total_flips - heads_count
        heads_pct = (heads_count / total_flips) * 100
        tails_pct = (tails_count / total_flips) * 100

        ax1.set_title(f"Coin Toss Outcomes Over Time (Total: {total_flips} flips)")
        ax1.set_xlabel("Flip Number")
        ax1.set_ylabel("Outcome")
        ax1.set_yticks([0, 1])
        ax1.set_yticklabels(['Tails', 'Heads'])
        ax1.grid(True, linestyle='--', alpha=0.7)

        # Annotate current percentages directly on ax1
        ax1.text(0.02, 0.98, f'Heads: {heads_pct:.2f}%', transform=ax1.transAxes, 
                 verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5', fc='blue', alpha=0.5))
        ax1.text(0.02, 0.90, f'Tails: {tails_pct:.2f}%', transform=ax1.transAxes, 
                 verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5', fc='black', alpha=0.5, ec='white', lw=1, color='white'))

        # Set x-limits to show only the last MAX_FLIPS_TO_DISPLAY
        if total_flips > MAX_FLIPS_TO_DISPLAY:
            ax1.set_xlim(total_flips - MAX_FLIPS_TO_DISPLAY, total_flips)
        else:
            ax1.set_xlim(0, total_flips)


    # --- Plotting Rolling Win Rate and Overall Win Rate (ax2) ---
    if win_loss_history:
        win_losses = list(win_loss_history)
        flip_numbers_ax2 = range(len(win_losses))

        # Calculate rolling win rate
        if len(win_losses) >= ROLLING_WINDOW_SIZE:
            rolling_wins = pd.Series(win_losses).rolling(window=ROLLING_WINDOW_SIZE).mean() * 100
            # Plot only from where the rolling window is full
            ax2.plot(flip_numbers_ax2[ROLLING_WINDOW_SIZE-1:], rolling_wins[ROLLING_WINDOW_SIZE-1:], 
                     label=f'Rolling Win Rate ({ROLLING_WINDOW_SIZE} flips)', color='purple')

        # Calculate overall win rate
        overall_win_rate = (sum(win_losses) / len(win_losses)) * 100
        ax2.axhline(overall_win_rate, color='green', linestyle='-', linewidth=2, label=f'Overall Win Rate: {overall_win_rate:.2f}%')

        ax2.set_title(f"Rolling Win Rate (Last {ROLLING_WINDOW_SIZE} Flips) & Overall Bias (Implied Bet: {'Heads' if YOUR_IMPLIED_BET == 1 else 'Tails'})")
        ax2.set_xlabel("Flip Number")
        ax2.set_ylabel("Percentage (%)")
        ax2.set_ylim(0, 100)
        ax2.axhline(50, color='gray', linestyle=':', linewidth=1, label='50% Mark') # 50% line
        ax2.grid(True, linestyle='--', alpha=0.7)
        ax2.legend()
        1
        # Set x-limits for ax2
        if len(win_losses) > MAX_FLIPS_TO_DISPLAY:
            ax2.set_xlim(len(win_losses) - MAX_FLIPS_TO_DISPLAY, len(win_losses))
        else:
            ax2.set_xlim(0, len(win_losses))


# --- Start the animation ---
ani = animation.FuncAnimation(fig, animate, interval=UPDATE_INTERVAL_MS, cache_frame_data=False)

plt.tight_layout() # Adjust subplot params for a tight layout
plt.show() # Display the plot